---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import ProjectCard from '../../components/ProjectCard.astro';
import { siteConfig } from '../../config/site';

const projects = await getCollection('portfolio');
const sortedProjects = projects.sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
---

<Layout title={`Portfolio | ${siteConfig.title}`}>
  <section class="portfolio hero-refined bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40 relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Modern background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="hero-title-refined">
        <h1 id="portfolio-title" class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          Portfolio
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded"></span>
        </h1>
        <p class="text-lg text-secondary-600 dark:text-secondary-400 max-w-2xl mx-auto leading-relaxed">
          A collection of scalable systems and innovative solutions I've architected and built
        </p>
      </div>
      
      <!-- Portfolio Projects Section with glass-card styling -->
      <div class="glass-card p-8 rounded-2xl">
        <div class="flex items-center gap-3 mb-8">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-secondary-800 dark:text-secondary-200 font-heading">Featured Projects</h2>
        </div>
        
        <div class="portfolio-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
          {sortedProjects.map((project, index) => {
            // Make first project featured in bento grid layout
            const isFeatured = index === 0;
            return (
              <a 
                href={`/portfolio/${project.slug}`} 
                class="block h-full"
                style={`animation-delay: ${index * 0.1}s;`}
                role="listitem"
              >
                <ProjectCard
                  title={project.data.title}
                  description={project.data.description}
                  tags={project.data.tags}
                  slug={project.slug}
                  image={project.data.image}
                  isFeatured={isFeatured}
                  technologies={project.data.technologies}
                  github={project.data.github}
                  live={project.data.live}
                  featured={project.data.featured}
                  problem={project.data.problem}
                  publishDate={project.data.publishDate}
                />
              </a>
            );
          })}
        </div>
      </div>

      {/* Back to home CTA */}
      <div class="text-center mt-16">
        <a href="/" class="btn-secondary group text-lg px-8 py-4">
          <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
          </svg>
          Back to Home
        </a>
      </div>
    </div>
  </section>
</Layout> 