# Portfolio Template Setup Guide

Welcome! This is a modern, customizable portfolio template built with Astro.js. Follow this guide to quickly personalize it for your own use.

## Quick Start (5 minutes)

**✨ NEW: Multi-Profession Support**
This template now supports multiple professions! Simply set your profession and the template automatically configures appropriate skills, descriptions, and content.

**Supported Professions:**
- `developer` - Software/Web Developer
- `designer` - UI/UX Designer  
- `marketer` - Digital Marketer
- `consultant` - Business Consultant
- `teacher` - Educator/Teacher

📖 **[View Complete Profession Guide →](PROFESSION_GUIDE.md)** - Detailed documentation with examples and customization options

### 1. Configure Your Information

Edit these main configuration files:

#### `src/config/site.ts` - Personal & Site Information
```typescript
export const siteConfig: SiteConfig = {
  // 👤 Personal Information - CHANGE THESE
  name: "Your Full Name",
  title: "Your Name | Your Professional Title", 
  description: "Professional with expertise in creating exceptional results",
  email: "<EMAIL>",
  domain: "https://yourdomain.com",
  
  // 💼 Professional Details
  profession: "developer", // 🎯 CHOOSE: 'developer', 'designer', 'marketer', 'consultant', 'teacher'
  jobTitle: "Your Professional Title",
  location: "Your City, Country",
  bio: "Brief professional description (auto-generated based on profession)",
  
  // 🔗 Social Links (remove or set to undefined to hide)
  social: {
    github: "https://github.com/yourusername",
    linkedin: "https://linkedin.com/in/yourusername", 
    twitter: "https://twitter.com/yourusername",
    // Optional: instagram, youtube, website
  },
  
  // 🧭 Navigation Menu
  navigation: [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Portfolio', href: '/portfolio' },
    { name: 'Resume', href: '/resume' },
    { name: 'Resources', href: '/resources' },
    { name: 'Contact', href: '/contact' },
  ],
  
  // 🎯 SEO Settings
  seo: {
    ogImage: '/images/og-image.jpg',
    twitterCard: 'summary_large_image',
    keywords: [
      'your', 'relevant', 'keywords', 'here'
    ],
  },
  
  // 🔗 GitHub Configuration (for portfolio projects)
  github: {
    username: "yourusername",
    baseUrl: "https://github.com/yourusername",
  },
};
```

#### `src/config/content.ts` - Skills & Experience (Auto-Generated! 🎉)
```typescript
// Content is automatically generated based on your profession!
// You can customize any of these auto-generated values:

export const contentConfig: ContentConfig = {
  // 🛠️ Skills - Auto-populated based on profession
  skills: dynamicContent.skills, // ← Automatically filled!
  
  // 📊 Skill Categories - Auto-generated labels
  skillCategories: dynamicContent.skillCategories, // ← Profession-specific!
  
  // ⭐ Experience Highlights - Auto-generated, customize as needed
  experienceHighlights: dynamicContent.experienceHighlights,
  
  // 📝 Professional Summary - Auto-generated
  professionalSummary: dynamicContent.professionalSummary,
  
  // 📖 About Page Content - Customize the personal touches
  about: {
    introduction: dynamicContent.aboutDescription, // ← Auto-generated
    expertise: dynamicContent.skills.primary.slice(0, 6), // ← From skills
    interests: [
      "Continuous learning",    // ← Customize these
      "Professional development", 
      "Industry trends",
      // Add your personal interests...
    ]
  },
};
```

**✨ That's it!** Just change your `profession` and everything updates automatically!

### 2. Add Your Profile Image

Replace `/public/images/profile.jpg` with your own photo.

### 3. Update Content Files

Edit the MDX files in `src/content/`:
- `src/content/homepage/main.md` - Homepage hero content
- `src/content/about/main.md` - About page details
- `src/content/portfolio/` - Add your project case studies

### 4. Deploy

```bash
npm run build
npm run preview  # Test locally
# Deploy to your preferred platform (Netlify, Vercel, etc.)
```

## Detailed Customization

### Adding New Skills

Add skills to any category in `src/config/content.ts`:

```typescript
skills: {
  backend: [
    "Node.js",
    "Your New Framework",  // ← Add here
    "Another Skill"
  ],
  // Or create new categories
  mobile: ["React Native", "Flutter"],
}
```

### Adding Social Links

Add new social platforms in `src/config/site.ts`:

```typescript
social: {
  github: "https://github.com/you",
  linkedin: "https://linkedin.com/in/you",
  youtube: "https://youtube.com/@you",     // ← Add new platforms
  instagram: "https://instagram.com/you",  // ← Like this
}
```

The Footer component will automatically display icons for: `linkedin`, `github`, `twitter`, `instagram`, `youtube`, `website`.

### Customizing Navigation

Modify the navigation menu in `src/config/site.ts`:

```typescript
navigation: [
  { name: 'Home', href: '/' },
  { name: 'Blog', href: '/blog' },      // ← Add new pages
  { name: 'Services', href: '/services' }, // ← Custom sections
  { name: 'Contact', href: '/contact' },
],
```

### Adding Portfolio Projects

Create new `.md` files in `src/content/portfolio/`:

```markdown
---
title: "Your Project Name"
description: "Brief project description"
publishDate: 2024-01-15
tags: ["React", "Node.js", "MongoDB"]
featured: true
demoUrl: "https://your-demo.com"
repoUrl: "https://github.com/you/project"
image: "/images/projects/your-project.jpg"
---

Your detailed project description here...
```

### Feature Toggles

Enable/disable features in `src/config/site.ts`:

```typescript
features: {
  darkMode: true,        // Dark/light theme toggle
  analytics: false,      // Analytics integration
  contactForm: true,     // Contact form functionality
  blog: false,          // Blog section
},
```

### SEO Optimization

1. **Meta Tags**: Automatically generated from `siteConfig`
2. **OG Image**: Replace `/public/images/og-image.jpg`
3. **Keywords**: Update in `siteConfig.seo.keywords`
4. **Structured Data**: Auto-generated person schema

### Color Scheme

The template uses a professional 2025 color palette. To customize:

1. Edit `tailwind.config.mjs` for colors
2. Modify CSS variables in `src/styles/design-tokens.css`

## File Structure

```
src/
├── config/
│   ├── site.ts          # 👈 Main site configuration
│   └── content.ts       # 👈 Skills & content configuration
├── content/             # 📝 MDX content files
│   ├── homepage/
│   ├── about/
│   ├── portfolio/
│   └── resources/
├── components/          # 🧩 Reusable components
├── layouts/            # 📐 Page layouts
├── pages/              # 📄 Route pages
└── styles/             # 🎨 Global styles
```

## Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run check        # Type checking and linting
```

## Performance Features

- ⚡ Astro's zero-JS approach for optimal loading
- 🎯 95+ Lighthouse scores out of the box
- 🖼️ Optimized image loading with Sharp
- 🌐 Service worker for caching
- 📱 Fully responsive design

## Getting Help

- **Documentation**: Check `CLAUDE.md` for detailed development info
- **Issues**: Report bugs or request features on GitHub
- **Customization**: Most changes only require editing the config files

## Deploy Options

**Recommended platforms:**
- [Netlify](https://netlify.com) - Drag & drop the `dist` folder
- [Vercel](https://vercel.com) - Connect your GitHub repo
- [GitHub Pages](https://pages.github.com) - Free hosting for public repos

All platforms work great with Astro's static output!

---

**That's it!** Your portfolio should now be personalized and ready to deploy. The template handles all the technical complexity while keeping customization simple.