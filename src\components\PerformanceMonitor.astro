---
// Performance monitoring component for Core Web Vitals
---

<script is:inline>
  // Performance monitoring for Core Web Vitals
  (function() {
    if (typeof window === 'undefined' || !window.performance) {
      return;
    }

    // Track page load performance
    function trackPageLoad() {
      if ('PerformanceObserver' in window) {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          entries.forEach((entry) => {
            console.log('FID:', entry.processingStart - entry.startTime);
          });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift (CLS)
        new PerformanceObserver((entryList) => {
          let clsValue = 0;
          const entries = entryList.getEntries();
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          console.log('CLS:', clsValue);
        }).observe({ entryTypes: ['layout-shift'] });
      }

      // Navigation timing
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            console.log('Navigation timing:', {
              'DNS lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
              'TCP connection': navigation.connectEnd - navigation.connectStart,
              'Request time': navigation.responseStart - navigation.requestStart,
              'Response time': navigation.responseEnd - navigation.responseStart,
              'DOM processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
              'Load event': navigation.loadEventEnd - navigation.loadEventStart,
              'Total time': navigation.loadEventEnd - navigation.navigationStart
            });
          }
        }, 1000);
      });
    }

    // Initialize performance tracking
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', trackPageLoad);
    } else {
      trackPageLoad();
    }
  })();
</script>