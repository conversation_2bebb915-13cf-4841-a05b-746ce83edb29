import { dynamicContent } from './content-generator';

export interface SkillCategory {
  name: string;
  skills: string[];
  icon?: string;
}

export interface ExperienceHighlight {
  text: string;
  icon?: string;
}

export interface ContentConfig {
  // Skills & Technologies (automatically populated based on profession)
  skills: {
    primary: string[];
    secondary: string[];
    tools: string[];
    soft: string[];
  };
  
  // Professional Experience Highlights
  experienceHighlights: ExperienceHighlight[];
  
  // Professional Summary
  professionalSummary: string;
  
  // About Page Content
  about: {
    introduction: string;
    expertise: string[];
    interests: string[];
  };
  
  // Resume Content
  resume: {
    summary: string;
    experienceAreas: {
      title: string;
      description: string;
    }[];
  };
  
  // Profession-specific labels
  skillCategories: {
    primary: string;
    secondary: string;
    tools: string;
    soft: string;
  };
}

// Content Configuration - Automatically configured based on profession
// You can override any of these values to customize your content
export const contentConfig: ContentConfig = {
  // Skills (automatically populated based on profession)
  skills: dynamicContent.skills,
  
  // Skill category labels (based on profession)
  skillCategories: dynamicContent.skillCategories,
  
  // Professional Experience Highlights (based on profession, customize as needed)
  experienceHighlights: dynamicContent.experienceHighlights,
  
  // Professional Summary (auto-generated, customize as needed)
  professionalSummary: dynamicContent.professionalSummary,
  
  // About Page Content (customize these for your personal touch)
  about: {
    introduction: dynamicContent.aboutDescription,
    expertise: dynamicContent.skills.primary.slice(0, 6), // First 6 primary skills as expertise
    interests: [
      "Continuous learning",
      "Professional development", 
      "Industry trends",
      "Best practices",
      "Knowledge sharing",
      "Innovation"
    ]
  },
  
  // Resume Page Content (auto-generated based on profession)
  resume: {
    summary: dynamicContent.professionalSummary,
    experienceAreas: dynamicContent.resumeExperienceAreas
  }
};

// Utility functions
export function getSkillsByCategory(category: keyof ContentConfig['skills']) {
  return contentConfig.skills[category];
}

export function getAllSkills() {
  return Object.values(contentConfig.skills).flat();
}

export function getExperienceHighlights() {
  return contentConfig.experienceHighlights;
}

export function getProfessionalSummary() {
  return contentConfig.professionalSummary;
}