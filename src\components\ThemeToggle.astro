---
// Modern Theme Toggle Switch v3.0 - 2025 Design
// Toggle switch with glassmorphism and smooth animations
---

<button
  id="theme-toggle"
  class="theme-toggle no-animation hidden"
  data-theme="light"
  aria-label="Toggle between light and dark theme"
  title="Switch theme"
  role="switch"
  aria-checked="false"
>
  <!-- Toggle Track -->
  <div class="toggle-track">
    <!-- Toggle Thumb -->
    <div class="toggle-thumb">
      <!-- Sun Icon -->
      <svg class="icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="4"/>
        <path d="M12 3v1M12 20v1M21 12h-1M4 12H3M18.364 5.636l-.707.707M6.343 17.657l-.707.707M5.636 5.636l.707.707M17.657 17.657l.707.707"/>
      </svg>
      <!-- <PERSON> Icon -->
      <svg class="icon moon-icon" viewBox="0 0 24 24" fill="currentColor">
        <path d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"/>
      </svg>
    </div>
  </div>
</button>

<script>
  // Modern Theme Toggle Switch v3.2 - HMR-resistant initialization
  function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle') as HTMLButtonElement;
    const html = document.documentElement;

    if (!themeToggle) return;

    // Prevent multiple event listeners during HMR
    if ((themeToggle as any).__THEME_TOGGLE_INITIALIZED__) {
      return;
    }

    // Use the initial theme state set by the head script to avoid conflicts
    const initialTheme = (window as any).__INITIAL_THEME__;
    const shouldUseDark = initialTheme ? initialTheme.theme === 'dark' : html.classList.contains('dark');

    // Disable animations during initialization to prevent visual jumps
    if (!themeToggle.classList.contains('no-animation')) {
      themeToggle.classList.add('no-animation');
    }
    
    // Sync toggle state with current theme (don't re-apply theme)
    themeToggle.setAttribute('aria-checked', shouldUseDark.toString());
    themeToggle.setAttribute('data-theme', shouldUseDark ? 'dark' : 'light');
    
    // Force a reflow to ensure initial state is applied without animation
    themeToggle.offsetHeight;
    
    // Show the button immediately after state is set (no transition will be visible)
    themeToggle.classList.remove('hidden');
    
    // Enable animations after a delay to ensure state is fully applied
    setTimeout(() => {
      themeToggle.classList.remove('no-animation');
      themeToggle.classList.add('animations-enabled');
    }, 150); // Longer delay to prevent any visual transition on page load
    
    // Add haptic feedback support for mobile
    const supportsHaptic = 'vibrate' in navigator;

    // Simple and reliable toggle function
    function toggleTheme(event: Event) {
      const isDark = html.classList.contains('dark');
      const newTheme = isDark ? 'light' : 'dark';

      // Apply theme change
      html.classList.toggle('dark', !isDark);

      // Safely store in localStorage with error handling
      try {
        localStorage.setItem('theme', newTheme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }

      themeToggle.setAttribute('aria-checked', (!isDark).toString());
      themeToggle.setAttribute('data-theme', newTheme);

      // Announce change for screen readers
      announceThemeChange(newTheme + ' mode');

      // Dispatch event for other components
      window.dispatchEvent(new CustomEvent('theme-changed', {
        detail: {
          theme: newTheme,
          source: 'user',
          timestamp: Date.now()
        }
      }));
    }


    // Announce theme changes to screen readers
    function announceThemeChange(theme: string) {
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.className = 'sr-only';
      announcement.textContent = `Switched to ${theme}`;
      document.body.appendChild(announcement);

      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    }

    // Simple event listeners
    themeToggle.addEventListener('click', toggleTheme);
    
    // Keyboard support
    themeToggle.addEventListener('keydown', (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        toggleTheme(event);
      }
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        html.classList.toggle('dark', e.matches);
        themeToggle.setAttribute('aria-checked', e.matches.toString());
        themeToggle.setAttribute('data-theme', e.matches ? 'dark' : 'light');

        window.dispatchEvent(new CustomEvent('theme-changed', {
          detail: {
            theme: e.matches ? 'dark' : 'light',
            source: 'system',
            timestamp: Date.now()
          }
        }));
      }
    });

    // Mark as initialized to prevent duplicate event listeners during HMR
    (themeToggle as any).__THEME_TOGGLE_INITIALIZED__ = true;
  }

  // Initialize with performance optimization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeToggle);
  } else {
    // Use requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      requestIdleCallback(initThemeToggle);
    } else {
      setTimeout(initThemeToggle, 0);
    }
  }

  // Re-initialize on Astro page navigation with proper animation handling
  document.addEventListener('astro:page-load', () => {
    // Clear any existing initialization flags for the new page
    const existingToggle = document.getElementById('theme-toggle');
    if (existingToggle) {
      (existingToggle as any).__THEME_TOGGLE_INITIALIZED__ = false;
      // Hide the toggle and reset its state for the new page
      existingToggle.classList.add('no-animation', 'hidden');
      existingToggle.classList.remove('animations-enabled');
    }
    
    // Initialize immediately to prevent any visual flicker
    initThemeToggle();
  });
</script>

<style>
  /* Fixed Modern Theme Toggle */
  .theme-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    outline: none;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    padding: 2px;
  }

  .theme-toggle[data-theme="dark"] {
    background: #374151;
  }

  .dark .theme-toggle {
    background: #374151;
  }

  /* Toggle Track */
  .toggle-track {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  /* Toggle Thumb */
  .toggle-thumb {
    position: relative;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateX(0);
  }

  /* Theme specific styles */
  .theme-toggle[data-theme="dark"] .toggle-thumb {
    background: #1f2937;
    transform: translateX(30px);
  }

  .dark .theme-toggle .toggle-thumb {
    background: #1f2937;
    transform: translateX(30px);
  }

  /* Icons */
  .icon {
    width: 14px;
    height: 14px;
    position: absolute;
  }

  .sun-icon {
    color: #f59e0b;
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }

  .moon-icon {
    color: #6366f1;
    opacity: 0;
    transform: scale(0.5) rotate(90deg);
  }

  /* Theme specific icon states */
  .theme-toggle[data-theme="dark"] .sun-icon,
  .dark .theme-toggle .sun-icon {
    opacity: 0;
    transform: scale(0.5) rotate(-90deg);
  }

  .theme-toggle[data-theme="dark"] .moon-icon,
  .dark .theme-toggle .moon-icon {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }


  /* Hover Effects */
  .theme-toggle:hover {
    transform: scale(1.05);
  }

  .theme-toggle:hover .toggle-thumb {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Active State */
  .theme-toggle:active {
    transform: scale(0.95);
  }

  /* Focus State */
  .theme-toggle:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px #3b82f6;
  }

  /* Disable animations during initialization and page load */
  .theme-toggle.no-animation,
  .theme-toggle.no-animation .toggle-thumb,
  .theme-toggle.no-animation .icon {
    transition: none !important;
  }

  /* Prevent animations during page loading */
  .theme-toggle {
    transition: none;
  }
  
  .theme-toggle .toggle-thumb {
    transition: none;
  }
  
  .theme-toggle .icon {
    transition: none;
  }

  /* Enable animations only after explicit activation */
  .theme-toggle.animations-enabled {
    transition: all 0.3s ease;
  }

  .theme-toggle.animations-enabled .toggle-thumb {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s ease;
  }

  .theme-toggle.animations-enabled .icon {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s ease;
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    .theme-toggle.animations-enabled,
    .theme-toggle.animations-enabled .toggle-thumb,
    .theme-toggle.animations-enabled .icon {
      transition: none !important;
    }
  }


  /* Hidden state for initialization */
  .theme-toggle.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .theme-toggle {
      width: 54px;
      height: 28px;
    }
    
    .toggle-thumb {
      width: 24px;
      height: 24px;
    }
    
    .theme-toggle[data-theme="dark"] .toggle-thumb {
      transform: translateX(26px);
    }
    
    .dark .theme-toggle .toggle-thumb {
      background: #1f2937;
      transform: translateX(26px);
    }
    
    .icon {
      width: 12px;
      height: 12px;
    }
  }
</style>
