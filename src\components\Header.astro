---
import ThemeToggle from './ThemeToggle.astro';
import { siteConfig, getNavigation } from '../config/site';

const currentPath = Astro.url.pathname;

const navigation = getNavigation().map(item => ({
  ...item,
  current: item.href === '/' ? currentPath === '/' : currentPath.startsWith(item.href)
}));
---

<header class="sticky top-0 z-50 w-full border-b border-secondary-200 dark:border-secondary-700 bg-background-light/80 dark:bg-background-dark/80 backdrop-blur-md transition-colors duration-300">
  <nav class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-2 group">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-500 text-white font-bold text-sm group-hover:bg-primary-600 transition-colors duration-200">
            {siteConfig.name.split(' ').map(n => n[0]).join('')}
          </div>
          <span class="hidden sm:block font-heading font-semibold text-text-light dark:text-text-dark text-lg">
            {siteConfig.name}
          </span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-4">
          {navigation.map((item) => (
            <a
              href={item.href}
              class={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                item.current
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'text-text-light-secondary dark:text-text-dark-secondary hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-text-light dark:hover:text-text-dark'
              }`}
              aria-current={item.current ? 'page' : undefined}
            >
              {item.name}
            </a>
          ))}
        </div>
      </div>

      <!-- Theme Toggle & Mobile Menu -->
      <div class="flex items-center space-x-2">
        <ThemeToggle />
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            type="button"
            class="inline-flex items-center justify-center rounded-md p-2 text-text-light-secondary dark:text-text-dark-secondary hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-text-light dark:hover:text-text-dark transition-colors duration-200"
            aria-controls="mobile-menu"
            aria-expanded="false"
            onclick="toggleMobileMenu()"
          >
            <span class="sr-only">Open main menu</span>
            <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="md:hidden hidden" id="mobile-menu">
      <div class="space-y-1 px-2 pb-3 pt-2">
        {navigation.map((item) => (
          <a
            href={item.href}
            class={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
              item.current
                ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                : 'text-text-light-secondary dark:text-text-dark-secondary hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-text-light dark:hover:text-text-dark'
            }`}
            aria-current={item.current ? 'page' : undefined}
          >
            {item.name}
          </a>
        ))}
      </div>
    </div>
  </nav>
</header>

<script is:inline>
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
      mobileMenu.classList.toggle('hidden');
    }
  }
</script>