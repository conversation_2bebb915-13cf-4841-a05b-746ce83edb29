/**
 * Utility functions for extracting images from URLs
 */

interface OpenGraphData {
  image?: string;
  title?: string;
  description?: string;
  siteName?: string;
}

// Cache for extracted images to avoid repeated processing
const imageCache = new Map<string, string>();

/**
 * Extract optimized image from a URL with caching
 */
export async function extractImageFromUrl(
  url: string,
  size: 'small' | 'medium' | 'large' = 'medium'
): Promise<string | null> {
  try {
    const cacheKey = `${url}-${size}`;

    // Check cache first
    if (imageCache.has(cacheKey)) {
      return imageCache.get(cacheKey)!;
    }

    // Get image from known domain with size optimization
    const imageUrl = getImageFromKnownDomain(url, size);

    // Cache the result
    if (imageUrl) {
      imageCache.set(cacheKey, imageUrl);
    }

    return imageUrl;
  } catch (error) {
    console.warn(`Failed to extract image from ${url}:`, error);
    return null;
  }
}

/**
 * Get optimized image from known domains with size variants
 */
function getImageFromKnownDomain(url: string, size: 'small' | 'medium' | 'large' = 'medium'): string | null {
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname.toLowerCase();

    // Size mappings for different use cases
    const sizeMap = {
      small: 32,   // Cards view thumbnails
      medium: 64,  // Default size
      large: 128   // Magazine view, high-DPI displays
    };

    const targetSize = sizeMap[size];

    // High-quality domain mappings with size variants
    const domainImages: Record<string, string> = {
      // Documentation sites - prefer SVG for scalability
      'docs.astro.build': 'https://docs.astro.build/favicon.svg',
      'developer.mozilla.org': `https://developer.mozilla.org/favicon-${Math.min(targetSize, 48)}x${Math.min(targetSize, 48)}.png`,
      'tailwindcss.com': 'https://tailwindcss.com/favicons/favicon.svg',
      'www.typescriptlang.org': `https://www.typescriptlang.org/favicon-${Math.min(targetSize, 32)}x${Math.min(targetSize, 32)}.png`,
      'typescriptlang.org': `https://www.typescriptlang.org/favicon-${Math.min(targetSize, 32)}x${Math.min(targetSize, 32)}.png`,

      // Design tools - high-quality logos
      'www.figma.com': targetSize > 64 ? 'https://static.figma.com/app/icon/1/icon-192.png' : 'https://static.figma.com/app/icon/1/favicon.png',
      'figma.com': targetSize > 64 ? 'https://static.figma.com/app/icon/1/icon-192.png' : 'https://static.figma.com/app/icon/1/favicon.png',
      'www.sketch.com': `https://www.sketch.com/images/pages/press/sketch-press-kit/app-icons/sketch-mac-icon-${targetSize > 64 ? '512' : '128'}.png`,
      'sketch.com': `https://www.sketch.com/images/pages/press/sketch-press-kit/app-icons/sketch-mac-icon-${targetSize > 64 ? '512' : '128'}.png`,

      // Development tools
      'github.com': targetSize > 64 ? 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png' : 'https://github.com/favicon.ico',
      'code.visualstudio.com': 'https://code.visualstudio.com/favicon.ico',
      'nodejs.org': 'https://nodejs.org/static/images/favicons/favicon.png',
      'www.npmjs.com': 'https://static.npmjs.com/b0f1a8318363185cc2ea6a40ac23eeb2.png',
      'npmjs.com': 'https://static.npmjs.com/b0f1a8318363185cc2ea6a40ac23eeb2.png',

      // Learning platforms
      'youtube.com': 'https://www.youtube.com/s/desktop/favicon.ico',
      'www.youtube.com': 'https://www.youtube.com/s/desktop/favicon.ico',
      'stackoverflow.com': 'https://cdn.sstatic.net/Sites/stackoverflow/Img/apple-touch-icon.png',
      'www.udemy.com': 'https://www.udemy.com/staticx/udemy/images/v7/logo-udemy.svg',
      'udemy.com': 'https://www.udemy.com/staticx/udemy/images/v7/logo-udemy.svg',

      // Cloud platforms
      'aws.amazon.com': 'https://a0.awsstatic.com/libra-css/images/site/fav/favicon.ico',
      'cloud.google.com': 'https://www.gstatic.com/devrel-devsite/prod/v2210deb8920cd4a55bd580441aa58e7853afc04b39a9d9ac4198e1cd7fbe04ef5/cloud/images/favicons/onecloud/favicon.ico',
      'azure.microsoft.com': 'https://azurecomcdn.azureedge.net/cvt-68b9d3e4b2c7b1c0e5b5b5b5b5b5b5b5b5b5b5b5/images/icon/favicon.ico',

      // Design resources
      'dribbble.com': 'https://cdn.dribbble.com/assets/favicon-b38525134603b7942c2c54a8b8c6c5e8.ico',
      'www.behance.net': 'https://a5.behance.net/2cb59c9/img/site/favicon.ico',
      'behance.net': 'https://a5.behance.net/2cb59c9/img/site/favicon.ico',
    };

    // Direct domain match
    if (domainImages[domain]) {
      return domainImages[domain];
    }

    // Pattern matching for subdomains
    for (const [pattern, imageUrl] of Object.entries(domainImages)) {
      if (domain.includes(pattern)) {
        return imageUrl;
      }
    }

    // Enhanced fallback with size optimization
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${targetSize}`;

  } catch (error) {
    console.warn(`Failed to parse URL ${url}:`, error);
    return null;
  }
}

/**
 * Generate a fallback image URL based on the first letter of the title
 * This creates a simple colored background with the first letter
 */
export function generateFallbackImage(title: string, category: string): string {
  const firstLetter = title.charAt(0).toUpperCase();
  const colors = {
    'documentation': '3B82F6', // blue
    'tools': '10B981', // emerald
    'learning': '8B5CF6', // purple
    'design': 'F59E0B', // amber
    'default': '6B7280' // gray
  };
  
  const color = colors[category.toLowerCase() as keyof typeof colors] || colors.default;
  
  // Using a simple SVG data URL for the fallback
  const svg = `
    <svg width="64" height="64" xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" fill="#${color}"/>
      <text x="32" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${firstLetter}</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

/**
 * Get the best available image for a resource with size optimization
 * Priority: manual override > extracted image > fallback
 */
export async function getResourceImage(
  url: string,
  title: string,
  category: string,
  manualImage?: string,
  size: 'small' | 'medium' | 'large' = 'medium'
): Promise<string> {
  // 1. Use manual override if provided
  if (manualImage) {
    return manualImage;
  }

  // 2. Try to extract from URL with size optimization
  const extractedImage = await extractImageFromUrl(url, size);
  if (extractedImage) {
    return extractedImage;
  }

  // 3. Fallback to generated image
  return generateFallbackImage(title, category);
}

/**
 * Get multiple image sizes for responsive loading
 */
export async function getResourceImageSet(
  url: string,
  title: string,
  category: string,
  manualImage?: string
): Promise<{
  small: string;
  medium: string;
  large: string;
}> {
  const [small, medium, large] = await Promise.all([
    getResourceImage(url, title, category, manualImage, 'small'),
    getResourceImage(url, title, category, manualImage, 'medium'),
    getResourceImage(url, title, category, manualImage, 'large')
  ]);

  return { small, medium, large };
}
