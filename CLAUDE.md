# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a modern portfolio website built with Astro.js 4.0+, TypeScript, and Tailwind CSS. The project showcases professional development practices with a focus on performance, accessibility, and modern web standards.

**Architecture:** Static site generation (SSG) with component-based architecture using Astro's zero-JS approach for optimal performance.

## Development Commands

### Core Development
```bash
# Start development server
npm run dev
# or
npm start

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking and linting
npm run check
```

### Performance Testing
```bash
# Run performance test suite
npm run perf:test

# Generate Lighthouse report (desktop)
npm run perf:lighthouse

# Generate Lighthouse report (mobile)
npm run perf:lighthouse:mobile

# Complete performance audit
npm run perf:audit
```

### Testing
```bash
# Run Playwright tests
npx playwright test

# Run specific test file
npx playwright test tests/homepage.spec.ts

# Run tests in headed mode
npx playwright test --headed

# Run tests in debug mode
npx playwright test --debug

# Theme reload testing
npx playwright test tests/theme-reload-test.spec.ts
```

## Architecture & Code Organization

### Framework Architecture
- **Framework:** Astro.js with zero-JavaScript approach for maximum performance
- **Styling:** Tailwind CSS with custom design system and dark mode support
- **Content:** MDX for structured content management
- **Type Safety:** Full TypeScript implementation

### Directory Structure
```
src/
├── components/          # Reusable Astro components (Footer, ProjectCard, ThemeToggle)
├── config/             # Configuration files
│   ├── site.ts         # Site configuration with dynamic labels system
│   ├── content.ts      # Content configuration and utilities
│   ├── content-generator.ts # Dynamic content generation
│   └── profession.ts   # Profession-specific content templates
├── content/            # MDX content files organized by section
│   ├── about/          # About page content
│   ├── contact/        # Contact page content with section headings
│   ├── homepage/       # Homepage content with dynamic sections
│   ├── portfolio/      # Project case studies
│   │   └── fallback/   # Professional fallback projects (auto-loads when needed)
│   └── resources/      # Resource bookmarks
├── layouts/            # Page layouts (Layout.astro with SEO and performance optimizations)
├── pages/              # File-based routing with dynamic content integration
├── styles/             # Global CSS and design tokens
└── utils/              # Utility functions
```

### Key Components
- **Layout.astro** (`src/layouts/Layout.astro`): Main layout with comprehensive SEO, performance monitoring, and theme system
- **Header.astro** (`src/components/Header.astro`): Navigation header with responsive mobile menu
- **Footer.astro** (`src/components/Footer.astro`): Site footer with enhanced styling and links
- **ThemeToggle.astro**: Enhanced dark/light mode toggle with animations and localStorage persistence
- **ProjectCard.astro**: Portfolio project display component with improved styling
- **PerformanceMonitor.astro**: Core Web Vitals monitoring component
- **ScrollAnimations.astro**: Scroll-based animations and micro-interactions

### Design System
- **Colors:** Professional 2025 palette with Mocha Mousse (#9e7a68) primary color
- **Typography:** Inter font family with systematic scale
- **Dark Mode:** Class-based dark mode with smooth transitions and improved toggle animations
- **Custom Tailwind:** Extended configuration in `tailwind.config.mjs` with comprehensive design tokens
- **Design Tokens:** Centralized design system in `src/styles/design-tokens.css`

## Development Practices

### Performance Requirements
- Target: 95+ Lighthouse score
- Load time: <1.2s
- Core Web Vitals optimization enabled
- Image optimization with Sharp

### Content Management
- **Dynamic System:** All UI text and headings managed through configuration and content files
- **Labels:** Use `src/config/site.ts` for UI text and button labels
- **Sections:** Use content files (`homepage/main.mdx`, `contact/main.mdx`) for page headings
- **Projects:** Use MDX files in `src/content/portfolio/` with automatic fallback system
- **Type Safety:** All content validated with Zod schemas in `src/content/config.ts`

### Component Development
- Follow Astro component patterns
- Use TypeScript interfaces for props
- Implement responsive design with Tailwind classes
- Ensure dark mode compatibility

### Theme System
- Theme initialization in `Layout.astro:109-161` prevents FOUC
- Theme state managed via localStorage and system preference detection
- Use semantic color tokens from `tailwind.config.mjs`

## Testing Strategy

### Test Categories
- **Homepage:** Core functionality and performance (`tests/homepage.spec.ts`)
- **Navigation:** Route testing and accessibility (`tests/navigation.spec.ts`)
- **Performance:** Core Web Vitals monitoring (`tests/performance.spec.ts`)
- **Theme:** Dark/light mode switching (`tests/theme-toggle.spec.ts`)

### Test Configuration
- Base URL: `http://localhost:4322` (Playwright config)
- Test environment: Chromium (Desktop Chrome)
- Retry strategy: 2 retries in CI, 0 locally

## SEO & Performance Features

### SEO Implementation
- Comprehensive meta tags and Open Graph data
- Structured data (JSON-LD) for person schema
- Canonical URLs and sitemap generation
- Optimized font loading with preconnect

### Performance Optimizations
- Service worker registration for caching
- Font display: swap for better LCP
- DNS prefetch for external resources
- Optimized image loading with Sharp

## Content Structure

### MDX Content Guidelines
- Place content files in appropriate `src/content/` subdirectories
- Use frontmatter for metadata (title, description, date, etc.)
- Follow existing content patterns for consistency
- Optimize images and place in `public/` directory

### Portfolio Projects
Portfolio items in `src/content/portfolio/` should include:
- Comprehensive project description
- Technology stack details
- Key achievements and metrics
- Links to live demos and repositories

### New Features & Pages
- **Theme Demo** (`/theme-demo`): Interactive theme system demonstration
- **Animation Test** (`/animation-test`): CSS animation and transition testing
- **Theme Test** (`/theme-test`): Theme toggle functionality testing
- **Enhanced Resources** (`/resources`): Comprehensive developer resource collection
- **Success Page** (`/success`): Form submission confirmation page

### Documentation
Enhanced project documentation includes:
- Theme system improvements documentation
- Theme toggle animation improvements guide
- Content planning and strategy documentation
- Design guidelines and component documentation

## Dynamic Content Management System

This portfolio website features a sophisticated dynamic content management system that eliminates hardcoded text and enables easy customization, internationalization, and content management.

### Configurable Labels System

**Location:** `src/config/site.ts` - `labels` configuration

The entire UI uses configurable labels instead of hardcoded text, enabling easy internationalization and customization.

#### Labels Configuration Structure
```typescript
labels: {
  navigation: {
    home: "Home",
    about: "About", 
    portfolio: "Portfolio",
    resume: "Resume",
    resources: "Resources",
    contact: "Contact"
  },
  sections: {
    aboutMe: "About Me",
    featuredProjects: "Featured Projects", 
    ctaHeading: "Ready to Build Something Amazing?",
    getInTouch: "Get in Touch",
    explore: "Explore"
  },
  buttons: {
    viewPortfolio: "View Portfolio",
    getInTouch: "Let's build something powerful",
    sendMessage: "Send Message",
    downloadResume: "Download Resume",
    viewAllProjects: "View All Projects",
    viewFullResume: "View Full Resume",
    startConversation: "Start a Conversation",
    backToTop: "Back to top"
  }
}
```

#### Usage Examples
- **Navigation:** Header and Footer components automatically use `labels.navigation.*`
- **Buttons:** All CTA buttons use `labels.buttons.*` 
- **Sections:** Page headings use `labels.sections.*`

#### Customization
To customize any UI text:
1. Edit the labels in `src/config/site.ts`
2. All components will automatically use the new text
3. Perfect for internationalization, A/B testing, or brand customization

### Section Headings Content Management

**Location:** Content files (`src/content/homepage/main.mdx`, `src/content/contact/main.mdx`)

Page headings and section titles are externalized to content files for complete content management.

#### Homepage Sections Configuration
```yaml
# src/content/homepage/main.mdx
---
sections:
  aboutMe:
    heading: "About Me"
    subheading: ""
  portfolio:
    heading: "Featured Projects"
    subheading: "A showcase of scalable systems and innovative solutions I've built"
  cta:
    heading: "Ready to Build Something Amazing?"
    subheading: "Let's discuss your next project and how I can help you build scalable, high-performance solutions."
---
```

#### Contact Page Configuration
```yaml
# src/content/contact/main.mdx
---
heading: "Get in Touch"
subheading: "I'm always open to discussing new projects, creative ideas, or opportunities to be part of your visions."
sections:
  email:
    title: "Email Me"
    description: "I'll get back to you as soon as possible."
  social:
    title: "Follow Me"
    description: "Connect with me on social media."
  form:
    title: "Send Message"
---
```

#### Benefits
- **Content-driven headings:** No hardcoded section titles in components
- **CMS-ready:** Content creators can modify headings without code changes
- **Version control:** Track content changes separately from code
- **Easy updates:** Change page headings by editing MDX files

### Fallback Portfolio Projects System

**Location:** `src/content/portfolio/fallback/`

Professional fallback projects are provided as MDX files when no real portfolio projects exist.

#### Fallback Project Structure
```yaml
---
title: "E-Commerce Platform Backend"
description: "Scalable microservices architecture for high-traffic e-commerce platform"
publishDate: 2024-01-01
technologies: ["Java", "Spring Boot", "Microservices", "PostgreSQL", "Redis", "Docker"]
tags: ["Backend", "Microservices", "Java", "API"]
featured: true
problem: "Built with modern technologies to handle thousands of concurrent users and process millions of transactions."
github: "https://github.com/example/ecommerce-backend"
live: "https://ecommerce-demo.example.com"
---

# E-Commerce Platform Backend

A scalable microservices architecture designed to power high-traffic e-commerce platforms...
```

#### How It Works
1. **Smart Detection:** `index.astro` checks for real portfolio projects
2. **Fallback Loading:** If no real projects exist, loads `fallback/` projects
3. **Seamless Integration:** Fallback projects use the same data structure as real projects
4. **Professional Presentation:** High-quality content that represents realistic projects

#### Available Fallback Projects
- **E-Commerce Backend:** Microservices architecture showcase
- **Real-time Analytics:** Event-driven analytics platform  
- **Cloud Infrastructure:** DevOps automation platform

#### Customization
To customize fallback projects:
1. Edit files in `src/content/portfolio/fallback/`
2. Add new fallback projects following the same frontmatter structure
3. Modify descriptions, technologies, and project details as needed

### Content Management Workflow

#### Adding New Content
1. **Labels:** Edit `src/config/site.ts` for UI text changes
2. **Headings:** Edit content files (`homepage/main.mdx`, `contact/main.mdx`) for section titles
3. **Projects:** Add real projects to `src/content/portfolio/` (fallbacks auto-hide)
4. **Pages:** Create new content collections in `src/content/config.ts`

#### Internationalization Setup
1. **Duplicate label configurations** for different languages
2. **Create language-specific content files**
3. **Use Astro's i18n features** with the existing label system
4. **Maintain consistent content structure** across languages

#### Content Management Integration
The architecture is designed for easy CMS integration:
- **Structured frontmatter** compatible with headless CMS
- **Clear content types** defined in `src/content/config.ts`
- **Separation of concerns** between content and code
- **Type-safe content** with Zod schema validation

### Migration Notes

When updating from older versions:
1. **Labels:** Existing hardcoded text replaced with configurable labels
2. **Content Files:** Section headings moved to content files  
3. **Fallback Projects:** Hardcoded fallback projects replaced with MDX files
4. **Backward Compatibility:** Fallbacks ensure no broken functionality

## Quick Reference

### Common Customization Tasks

#### Change Navigation Menu Labels
**File:** `src/config/site.ts`
```typescript
labels: {
  navigation: {
    home: "Home",           // ← Edit these labels
    about: "About",         // ← Edit these labels  
    portfolio: "Portfolio", // ← Edit these labels
    // ...
  }
}
```

#### Change Page Headings
**File:** `src/content/homepage/main.mdx`
```yaml
sections:
  aboutMe:
    heading: "About Me"     # ← Edit this heading
  portfolio:
    heading: "Featured Projects"  # ← Edit this heading
```

#### Change Button Text
**File:** `src/config/site.ts`
```typescript
labels: {
  buttons: {
    viewPortfolio: "View Portfolio",           # ← Edit button text
    startConversation: "Start a Conversation" # ← Edit button text
  }
}
```

#### Add Portfolio Projects
1. Create `.mdx` file in `src/content/portfolio/`
2. Use this frontmatter structure:
```yaml
---
title: "Project Name"
description: "Brief description"
publishDate: 2024-01-01
technologies: ["Tech1", "Tech2"]
tags: ["Category", "Type"]
featured: true
github: "https://github.com/..."
live: "https://demo.com"
---
```

#### Customize Contact Page
**File:** `src/content/contact/main.mdx`
```yaml
---
heading: "Get in Touch"        # ← Edit main heading
subheading: "Contact intro"    # ← Edit description
sections:
  email:
    title: "Email Me"          # ← Edit section title
---
```

## Development Notes
- **No need to run test**: Reminder to skip test runs in certain scenarios
- **Dynamic Content**: All UI text is configurable - no hardcoded strings in components
- **Fallback Projects**: Professional examples auto-load when no real projects exist
- **Type Safety**: All content changes are validated with TypeScript and Zod schemas