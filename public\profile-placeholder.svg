<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="#f1f5f9"/>
  
  <!-- Gradient overlay -->
  <defs>
    <linearGradient id="profileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9e7a68;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#8b5a4a;stop-opacity:0.2"/>
    </linearGradient>
  </defs>
  <circle cx="100" cy="100" r="100" fill="url(#profileGradient)"/>
  
  <!-- Professional icon -->
  <g transform="translate(50, 50)">
    <!-- Head -->
    <circle cx="50" cy="35" r="20" fill="#9e7a68"/>
    
    <!-- Body -->
    <path d="M30 85 C30 70, 35 60, 50 60 C65 60, 70 70, 70 85 L70 100 L30 100 Z" fill="#9e7a68"/>
    
    <!-- Suit/Shirt detail -->
    <path d="M40 75 C45 70, 55 70, 60 75 L60 100 L40 100 Z" fill="#7c5f4f"/>
  </g>
  
  <!-- Text -->
  <text x="100" y="170" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" fill="#64748b">
    Professional Photo
  </text>
</svg>