---
import Layout from '../../layouts/Layout.astro';
import { getCollection, type CollectionEntry } from 'astro:content';
import { siteConfig } from '../../config/site';

export async function getStaticPaths() {
  const projects = await getCollection('portfolio');
  return projects.map(project => ({
    params: { slug: project.slug },
    props: { project },
  }));
}

interface Props {
  project: CollectionEntry<'portfolio'>;
}

const { project } = Astro.props;
const { Content } = await project.render();
---

<Layout title={`${project.data.title} | Portfolio | ${siteConfig.name}`}>
  <article class="pt-32 pb-24 bg-background-light dark:bg-background-dark relative overflow-hidden">
    <!-- Modern background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <!-- Project Header -->
      <header class="mb-16 animate-fade-in">
        <div class="mb-8">
          <a href="/portfolio" class="group inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-all duration-300">
            <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
            </svg>
            Back to Portfolio
          </a>
        </div>
        
        <h1 class="heading-xl text-secondary-800 dark:text-secondary-200 mb-8 relative">
          {project.data.title}
          <span class="absolute bottom-0 left-0 -mb-3 w-24 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded"></span>
        </h1>
        
        <div class="flex flex-wrap items-center gap-6 text-sm text-secondary-600 dark:text-secondary-400 mb-10">
          <span class="flex items-center gap-2">
            <div class="w-5 h-5 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
            </div>
            {project.data.publishDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </span>
          <span class="flex items-center gap-2">
            <div class="w-5 h-5 bg-gradient-to-br from-secondary-500 to-primary-500 rounded-lg flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
              </svg>
            </div>
            {project.data.role}
          </span>
        </div>

        <div class="flex flex-wrap gap-3 mb-10">
          {project.data.technologies.map((tech, index) => (
            <span 
              class={`px-4 py-2 bg-gradient-to-r from-primary-500/10 to-accent-500/10 dark:from-primary-400/20 dark:to-accent-400/20 text-primary-600 dark:text-primary-300 text-sm font-medium rounded-full border border-primary-500/20 dark:border-primary-400/30 hover:bg-gradient-to-r hover:from-primary-500/20 hover:to-accent-500/20 dark:hover:from-primary-400/30 dark:hover:to-accent-400/30 transition-all duration-300 hover:scale-105 animate-slide-up`}
              style={`animation-delay: ${index * 0.1}s;`}
            >
              {tech}
            </span>
          ))}
        </div>
      </header>

      <!-- Project Overview Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16 animate-slide-up" style="animation-delay: 0.2s;">
        <div class="glass-card p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center text-white">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200 font-heading">Problem</h3>
          </div>
          <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed">{project.data.problem}</p>
        </div>
        
        <div class="glass-card p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center text-white">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200 font-heading">Solution</h3>
          </div>
          <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed">{project.data.solution}</p>
        </div>
        
        <div class="glass-card p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center text-white">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200 font-heading">Results</h3>
          </div>
          <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed">{project.data.results}</p>
        </div>
      </div>

      <!-- Project Links -->
      {(project.data.repoUrl || project.data.liveUrl) && (
        <div class="flex flex-wrap gap-6 mb-16 justify-center animate-slide-up" style="animation-delay: 0.4s;">
          {project.data.repoUrl && (
            <a href={project.data.repoUrl} target="_blank" rel="noopener noreferrer" 
               class="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-primary-500 to-accent-500 text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg">
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              View Repository
            </a>
          )}
          {project.data.liveUrl && (
            <a href={project.data.liveUrl} target="_blank" rel="noopener noreferrer"
               class="group inline-flex items-center gap-3 px-8 py-4 border-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400 rounded-2xl font-semibold hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 hover:-translate-y-1 text-lg">
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Live Demo
            </a>
          )}
        </div>
      )}

      <!-- Project Content -->
      <div class="glass-card p-10 lg:p-12 rounded-2xl mb-16 animate-slide-up" style="animation-delay: 0.6s;">
        <div class="prose prose-lg max-w-none dark:prose-invert prose-headings:font-heading prose-primary">
          <Content />
        </div>
      </div>

      <!-- Navigation -->
      <div class="text-center animate-slide-up" style="animation-delay: 0.8s;">
        <a href="/portfolio" class="btn-secondary group text-lg px-8 py-4">
          <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
          </svg>
          Back to Portfolio
        </a>
      </div>
    </div>
  </article>
</Layout> 