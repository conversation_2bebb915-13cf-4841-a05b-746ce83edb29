# Configuration Examples

This file provides real-world examples of how to configure your portfolio template for different professional profiles.

## Frontend Developer Example

```typescript
// src/config/site.ts
export const siteConfig: SiteConfig = {
  name: "<PERSON>",
  title: "<PERSON> | Frontend Developer & UI/UX Designer",
  description: "Frontend developer specializing in React, modern CSS, and user experience design.",
  email: "<EMAIL>",
  domain: "https://alexchen.dev",
  
  jobTitle: "Senior Frontend Developer",
  location: "San Francisco, CA",
  bio: "Frontend developer passionate about creating beautiful, accessible user interfaces and seamless user experiences.",
  
  social: {
    github: "https://github.com/alexchen",
    linkedin: "https://linkedin.com/in/alexchen-frontend",
    website: "https://dribbble.com/alexchen",
  },
  
  seo: {
    keywords: [
      'frontend developer',
      'react developer', 
      'ui ux designer',
      'javascript',
      'css',
      'web development'
    ],
  },
};

// src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    frontend: [
      "React & Next.js",
      "Vue.js & Nuxt.js",
      "TypeScript",
      "Modern CSS & Sass",
      "Tailwind CSS",
      "Responsive Design"
    ],
    backend: [
      "Node.js",
      "REST APIs",
      "GraphQL",
      "Firebase",
      "Supabase"
    ],
    architecture: [
      "Component Design Systems",
      "JAMstack Architecture", 
      "Performance Optimization",
      "Accessibility (a11y)",
      "Progressive Web Apps"
    ],
    devops: [
      "Netlify",
      "Vercel", 
      "GitHub Actions",
      "Docker",
      "CDN Optimization"
    ],
  },
  
  experienceHighlights: [
    { text: "Built responsive web apps used by 50,000+ users daily" },
    { text: "Improved page load speeds by 60% through optimization" },
    { text: "Created design systems adopted across multiple teams" },
    { text: "Led accessibility initiatives achieving WCAG 2.1 AA compliance" },
  ],
};
```

## Backend/DevOps Engineer Example

```typescript
// src/config/site.ts
export const siteConfig: SiteConfig = {
  name: "Maria Rodriguez",
  title: "Maria Rodriguez | Backend Engineer & DevOps Specialist",
  description: "Backend engineer focused on scalable systems, cloud infrastructure, and DevOps automation.",
  email: "<EMAIL>",
  domain: "https://mariarodriguez.tech",
  
  jobTitle: "Senior Backend Engineer",
  location: "Austin, TX",
  bio: "Backend engineer passionate about building robust, scalable systems and automating infrastructure for high-performance applications.",
  
  social: {
    github: "https://github.com/mariarodriguez",
    linkedin: "https://linkedin.com/in/maria-rodriguez-backend",
  },
  
  seo: {
    keywords: [
      'backend engineer',
      'devops engineer',
      'cloud infrastructure', 
      'kubernetes',
      'python',
      'go'
    ],
  },
};

// src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    frontend: [
      "React (Basic)",
      "HTML/CSS",
      "Admin Dashboards"
    ],
    backend: [
      "Python & Django/FastAPI",
      "Go & Gin",
      "Node.js",
      "PostgreSQL & MongoDB", 
      "Redis & Elasticsearch",
      "Message Queues (RabbitMQ, Kafka)"
    ],
    architecture: [
      "Microservices Architecture",
      "Event-Driven Systems",
      "Database Design & Optimization",
      "Caching Strategies",
      "Load Balancing",
      "API Gateway Design"
    ],
    devops: [
      "AWS & Google Cloud",
      "Kubernetes & Docker",
      "Terraform & Ansible",
      "CI/CD Pipelines",
      "Monitoring (Prometheus, Grafana)",
      "Infrastructure as Code"
    ],
  },
  
  experienceHighlights: [
    { text: "Architected microservices handling 10M+ API requests daily" },
    { text: "Reduced infrastructure costs by 40% through optimization" },
    { text: "Implemented zero-downtime deployment pipelines" },
    { text: "Built monitoring systems catching 99% of issues proactively" },
  ],
};
```

## Full-Stack Developer Example

```typescript
// src/config/site.ts
export const siteConfig: SiteConfig = {
  name: "Jordan Kim",
  title: "Jordan Kim | Full-Stack Developer & Tech Lead",
  description: "Full-stack developer building end-to-end web applications with modern technologies.",
  email: "<EMAIL>",
  domain: "https://jordankim.io",
  
  jobTitle: "Senior Full-Stack Developer",
  location: "Seattle, WA",
  bio: "Full-stack developer who loves building complete solutions from frontend to backend, with a focus on user experience and system reliability.",
  
  social: {
    github: "https://github.com/jordankim",
    linkedin: "https://linkedin.com/in/jordan-kim-fullstack",
    twitter: "https://twitter.com/jordankim_dev",
  },
  
  seo: {
    keywords: [
      'full stack developer',
      'web developer',
      'react developer',
      'node.js developer',
      'javascript',
      'typescript'
    ],
  },
};

// src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    frontend: [
      "React & Next.js",
      "TypeScript",
      "Tailwind CSS",
      "State Management (Redux, Zustand)",
      "Testing (Jest, Cypress)",
      "Mobile (React Native)"
    ],
    backend: [
      "Node.js & Express",
      "Python & FastAPI",
      "PostgreSQL & MongoDB",
      "RESTful APIs & GraphQL",
      "Authentication & Authorization",
      "Real-time Features (WebSockets)"
    ],
    architecture: [
      "Full-Stack Architecture",
      "Database Design",
      "API Design",
      "Performance Optimization",
      "Security Best Practices",
      "Scalability Planning"
    ],
    devops: [
      "AWS & Heroku",
      "Docker",
      "CI/CD (GitHub Actions)",
      "Monitoring & Analytics",
      "Version Control (Git)",
      "Agile Development"
    ],
  },
  
  experienceHighlights: [
    { text: "Led development of e-commerce platform serving 100K+ users" },
    { text: "Built real-time collaboration features for remote teams" },
    { text: "Mentored 5+ junior developers in full-stack development" },
    { text: "Shipped 15+ production applications with 99.9% uptime" },
  ],
};
```

## Data Scientist/ML Engineer Example

```typescript
// src/config/site.ts
export const siteConfig: SiteConfig = {
  name: "Dr. Sarah Patel",
  title: "Dr. Sarah Patel | Data Scientist & ML Engineer",
  description: "Data scientist specializing in machine learning, AI applications, and data-driven insights.",
  email: "<EMAIL>",
  domain: "https://sarahpatel.ai",
  
  jobTitle: "Senior Data Scientist",
  location: "Boston, MA",
  bio: "Data scientist passionate about turning complex data into actionable insights and building intelligent systems that solve real-world problems.",
  
  social: {
    github: "https://github.com/sarahpatel",
    linkedin: "https://linkedin.com/in/dr-sarah-patel",
    website: "https://scholar.google.com/citations?user=sarahpatel",
  },
  
  seo: {
    keywords: [
      'data scientist',
      'machine learning engineer',
      'artificial intelligence',
      'python',
      'deep learning',
      'data analysis'
    ],
  },
};

// src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    frontend: [
      "Streamlit & Dash",
      "React (Basic)",
      "Data Visualization",
      "Jupyter Notebooks"
    ],
    backend: [
      "Python & FastAPI",
      "R & Shiny",
      "SQL & NoSQL",
      "APIs for ML Models",
      "Data Pipelines"
    ],
    architecture: [
      "ML Model Architecture",
      "Data Pipeline Design",
      "Feature Engineering",
      "Model Deployment",
      "A/B Testing",
      "MLOps"
    ],
    devops: [
      "AWS ML Services",
      "Docker & Kubernetes",
      "MLflow & Kubeflow",
      "CI/CD for ML",
      "Model Monitoring",
      "Cloud Platforms"
    ],
    languages: [
      "Python",
      "R", 
      "SQL",
      "Scala",
      "Julia"
    ],
    frameworks: [
      "TensorFlow & PyTorch",
      "Scikit-learn",
      "Pandas & NumPy",
      "Apache Spark",
      "Hugging Face",
      "MLflow"
    ]
  },
  
  experienceHighlights: [
    { text: "Built ML models improving business outcomes by 25%" },
    { text: "Published 8 papers in top-tier AI conferences" },
    { text: "Led data science team of 6 researchers" },
    { text: "Deployed production ML systems serving 1M+ predictions daily" },
  ],
};
```

## Freelancer/Consultant Example

```typescript
// src/config/site.ts
export const siteConfig: SiteConfig = {
  name: "Taylor Johnson",
  title: "Taylor Johnson | Web Development Consultant",
  description: "Freelance web developer helping businesses build modern, performant web applications.",
  email: "<EMAIL>",
  domain: "https://taylorjohnson.dev",
  
  jobTitle: "Freelance Web Developer",
  location: "Remote (US/EU)",
  bio: "Freelance web developer specializing in helping startups and small businesses build their digital presence with modern, scalable solutions.",
  
  social: {
    github: "https://github.com/taylorjohnson",
    linkedin: "https://linkedin.com/in/taylor-johnson-dev",
    website: "https://taylorjohnson.dev/blog",
  },
  
  navigation: [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Portfolio', href: '/portfolio' },
    { name: 'Blog', href: '/blog' },
    { name: 'Contact', href: '/contact' },
  ],
  
  features: {
    darkMode: true,
    analytics: true,
    contactForm: true,
    blog: true,
  },
};

// src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    frontend: [
      "React & Next.js",
      "Vue.js",
      "Modern JavaScript/TypeScript",
      "Responsive Design",
      "Performance Optimization"
    ],
    backend: [
      "Node.js",
      "Python",
      "Serverless Functions",
      "API Development",
      "Database Design"
    ],
    architecture: [
      "JAMstack Solutions",
      "E-commerce Platforms",
      "CMS Integration", 
      "Performance Optimization",
      "SEO Implementation"
    ],
    devops: [
      "Netlify & Vercel",
      "AWS Basics",
      "CI/CD Setup",
      "Domain & Hosting",
      "Analytics Setup"
    ],
  },
  
  experienceHighlights: [
    { text: "Delivered 30+ successful projects for clients worldwide" },
    { text: "Specialized in e-commerce solutions generating $2M+ revenue" },
    { text: "Built high-performance websites scoring 95+ on Lighthouse" },
    { text: "Maintained 100% client satisfaction rating over 3 years" },
  ],
  
  about: {
    introduction: "I help businesses succeed online by building fast, beautiful, and functional websites. With over 5 years of experience, I specialize in creating custom solutions that drive results.",
    expertise: [
      "Custom web application development",
      "E-commerce platform building", 
      "Website performance optimization",
      "SEO and digital marketing integration",
      "Ongoing support and maintenance",
      "Technology consulting and strategy"
    ],
    interests: [
      "Latest web technologies",
      "Small business growth",
      "Remote work culture",
      "Open source contributions",
      "Web performance optimization",
      "Digital accessibility"
    ]
  },
};
```

## Tips for Your Configuration

1. **Be Specific**: Use concrete numbers and achievements in your experience highlights
2. **Stay Current**: List technologies you actually use and are comfortable with
3. **Match Your Role**: Tailor skills and content to your target position
4. **Keep it Honest**: Only list skills you can confidently discuss in an interview
5. **Update Regularly**: Keep your content fresh with new projects and skills

Choose the example closest to your profile and adapt it to your specific experience and goals!