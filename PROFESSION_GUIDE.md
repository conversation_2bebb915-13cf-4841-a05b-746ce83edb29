# Multi-Profession Portfolio Guide

This template supports multiple professions out of the box! Simply choose your profession and the entire portfolio automatically configures with appropriate skills, descriptions, and industry-specific content.

## Quick Start

### 1. Choose Your Profession

In `src/config/site.ts`, set your profession:

```typescript
export const siteConfig: SiteConfig = {
  // Personal Information
  name: "Your Name",
  title: "Your Name | Your Professional Title",
  // ... other config
  
  // 🎯 Choose your profession here
  profession: "designer", // Options: 'developer', 'designer', 'marketer', 'consultant', 'teacher'
  
  jobTitle: "Your Professional Title",
  // ... rest of config
};
```

### 2. That's It!

The template automatically updates:
- ✅ **Skills sections** with profession-appropriate categories
- ✅ **Experience highlights** relevant to your field
- ✅ **Professional descriptions** and CTAs
- ✅ **SEO keywords** for your industry
- ✅ **Skill category labels** (e.g., "Design & User Experience" vs "Programming & Development")

## Supported Professions

### 👨‍💻 Developer (`profession: "developer"`)
**Perfect for:** Software developers, web developers, full-stack developers

**Auto-generated content:**
- **Skills:** JavaScript, React, Node.js, System Design, Docker, AWS
- **Categories:** "Programming & Development", "Architecture & Systems", "Tools & Frameworks"
- **Experience:** "Built scalable web applications", "Optimized performance by 40%"
- **Keywords:** software developer, javascript, react, programming

### 🎨 Designer (`profession: "designer"`)
**Perfect for:** UI/UX designers, product designers, visual designers

**Auto-generated content:**
- **Skills:** UI Design, UX Research, Prototyping, Figma, Adobe Creative Suite
- **Categories:** "Design & User Experience", "Research & Strategy", "Design Tools & Software"
- **Experience:** "Redesigned interfaces increasing conversion by 35%", "Created design systems"
- **Keywords:** ui ux designer, user experience, design systems, prototyping

### 📱 Marketer (`profession: "marketer"`)
**Perfect for:** Digital marketers, content marketers, social media managers

**Auto-generated content:**
- **Skills:** Digital Marketing, SEO/SEM, Content Strategy, Google Analytics, Social Media
- **Categories:** "Marketing & Strategy", "Analytics & Optimization", "Marketing Tools & Platforms"
- **Experience:** "Increased organic traffic by 150%", "Generated $500K+ in revenue"
- **Keywords:** digital marketer, content marketing, seo, social media

### 💼 Consultant (`profession: "consultant"`)
**Perfect for:** Business consultants, management consultants, strategy consultants

**Auto-generated content:**
- **Skills:** Strategic Planning, Business Analysis, Process Improvement, Financial Modeling
- **Categories:** "Strategy & Analysis", "Process & Operations", "Business Tools & Methodologies"
- **Experience:** "Achieved 30% cost reduction", "Led digital transformation initiatives"
- **Keywords:** business consultant, strategy consultant, process improvement

### 👩‍🏫 Teacher (`profession: "teacher"`)
**Perfect for:** Teachers, educators, curriculum developers, instructional designers

**Auto-generated content:**
- **Skills:** Curriculum Development, Lesson Planning, Educational Technology, Assessment Design
- **Categories:** "Teaching & Curriculum", "Learning & Assessment", "Educational Technology"
- **Experience:** "Improved student test scores by 25%", "Developed curriculum for 500+ students"
- **Keywords:** teacher, educator, curriculum developer, education technology

## What Gets Auto-Generated

When you set your profession, the following content is automatically configured:

### Skills & Categories
```typescript
// For developers:
skills: {
  primary: ["JavaScript", "Python", "React", "Node.js", "TypeScript"],
  secondary: ["System Design", "Database Design", "API Development"],
  tools: ["Git", "Docker", "AWS", "VS Code"],
  soft: ["Problem Solving", "Team Collaboration", "Code Review"]
}

// For designers:
skills: {
  primary: ["UI Design", "UX Research", "Prototyping", "User Testing"],
  secondary: ["Design Systems", "Information Architecture", "Wireframing"],
  tools: ["Figma", "Sketch", "Adobe Creative Suite", "Principle"],
  soft: ["Creative Thinking", "Client Communication", "Design Critique"]
}
```

### Professional Content
```typescript
// Hero subtitle, about description, portfolio description
heroSubtitle: "Building innovative software solutions", // for developers
heroSubtitle: "Creating beautiful, intuitive experiences", // for designers

// Experience highlights
experienceHighlights: [
  "Built scalable web applications serving thousands of users", // developer
  "Redesigned user interfaces increasing conversion rates by 35%" // designer
]
```

### SEO & Keywords
```typescript
seoKeywords: [
  "software developer", "javascript", "react" // for developers
  "ui ux designer", "user experience", "design systems" // for designers
]
```

## Customization Options

### Override Auto-Generated Content

You can customize any auto-generated content in `src/config/content.ts`:

```typescript
export const contentConfig: ContentConfig = {
  // Use auto-generated skills or override specific ones
  skills: {
    ...dynamicContent.skills, // Keep auto-generated
    primary: ["Your", "Custom", "Skills"], // Override primary skills
  },
  
  // Override experience highlights
  experienceHighlights: [
    { text: "Your custom achievement #1" },
    { text: "Your custom achievement #2" },
    // Mix with auto-generated: ...dynamicContent.experienceHighlights
  ],
  
  // Override professional summary
  professionalSummary: "Your custom professional summary here",
  
  // Customize about section
  about: {
    introduction: dynamicContent.aboutDescription, // Keep auto-generated
    expertise: ["Custom", "Expertise", "Areas"], // Override
    interests: [
      "Your personal interests",
      "Hobbies and passions",
      "Professional development"
    ]
  }
};
```

### Custom Bio

In `src/config/site.ts`, you can override the auto-generated bio:

```typescript
export const siteConfig: SiteConfig = {
  profession: "developer",
  bio: "Your custom bio here instead of auto-generated one",
  // ... rest of config
};
```

## Adding New Professions

Want to add support for a new profession? Here's how:

### 1. Add Profession Profile

In `src/config/profession.ts`, add your new profession to the `professionProfiles` object:

```typescript
export const professionProfiles: Record<string, ProfessionProfile> = {
  // ... existing professions
  
  photographer: {
    name: "Photographer",
    titles: [
      "Photographer",
      "Portrait Photographer",
      "Wedding Photographer",
      "Commercial Photographer",
      "Photo Editor"
    ],
    skillCategories: {
      primary: "Photography & Composition",
      secondary: "Post-Processing & Editing",
      tools: "Camera Equipment & Software",
      soft: "Professional Skills"
    },
    commonSkills: {
      primary: ["Portrait Photography", "Landscape Photography", "Studio Lighting", "Composition", "Color Theory"],
      secondary: ["Photo Editing", "Digital Retouching", "Color Grading", "Print Preparation"],
      tools: ["Adobe Lightroom", "Adobe Photoshop", "Capture One", "Canon/Nikon", "Studio Equipment"],
      soft: ["Client Communication", "Creative Direction", "Time Management", "Attention to Detail"]
    },
    seoKeywords: [
      "photographer", "portrait photographer", "wedding photographer",
      "photography services", "photo editing", "professional photographer"
    ],
    experienceHighlights: [
      "Captured 200+ weddings and events with 100% client satisfaction",
      "Built photography business serving 50+ corporate clients",
      "Featured in 5+ photography magazines and exhibitions",
      "Developed signature editing style recognized by industry peers"
    ],
    heroSubtitle: "Capturing life's precious moments through creative storytelling",
    aboutDescription: "Professional photographer passionate about capturing authentic moments and creating timeless images that tell compelling stories.",
    portfolioDescription: "A collection of photography work spanning portraits, events, and commercial projects",
    ctaText: {
      primary: "View My Gallery",
      secondary: "Book a Session"
    },
    resourcesDescription: "Photography tips, tutorials, and industry insights",
    projectTypes: ["Portraits", "Weddings", "Events", "Commercial", "Fine Art"]
  }
};
```

### 2. Update Type Definitions

Add your new profession to the TypeScript comments in `src/config/site.ts`:

```typescript
export interface SiteConfig {
  // Professional
  profession: string; // 'developer', 'designer', 'marketer', 'consultant', 'teacher', 'photographer'
  // ... rest of interface
}
```

### 3. Update Documentation

Add your new profession to this guide and to `SETUP.md`.

## Advanced Customization

### Conditional Content Based on Profession

You can create profession-specific content in your components:

```typescript
// In any .astro file
---
import { siteConfig } from '../config/site';
import { getProfessionProfile } from '../config/profession';

const profession = getProfessionProfile(siteConfig.profession);
---

<!-- Conditional content -->
{siteConfig.profession === 'developer' && (
  <div>Special content for developers</div>
)}

{siteConfig.profession === 'designer' && (
  <div>Special content for designers</div>
)}

<!-- Use profession-specific data -->
<h2>{profession.ctaText.primary}</h2>
<p>{profession.heroSubtitle}</p>
```

### Mixed Profession Support

If you work across multiple fields, you can mix professions:

```typescript
// In src/config/content.ts
export const contentConfig: ContentConfig = {
  skills: {
    primary: [
      ...dynamicContent.skills.primary, // Your main profession skills
      "Cross-functional Collaboration", // Add cross-cutting skills
      "Project Management"
    ],
    secondary: [
      ...getProfessionProfile('designer').commonSkills.secondary, // Mix from another profession
      ...dynamicContent.skills.secondary
    ]
  }
};
```

## Best Practices

### 1. Start with Closest Match
Choose the profession closest to your primary role, then customize as needed.

### 2. Customize Gradually
Use the auto-generated content first, then override specific sections that need personalization.

### 3. Keep Industry Relevance
When adding custom skills or content, ensure they're relevant to your target audience and industry.

### 4. Test SEO Keywords
The auto-generated keywords are optimized for each profession, but you can add specific ones relevant to your niche.

### 5. Update Portfolio Projects
Remember to update your portfolio projects to match your chosen profession's project types.

## Troubleshooting

### Profession Not Working?
1. Check that your profession string exactly matches one in `professionProfiles`
2. Restart your dev server after changing profession
3. Clear browser cache if content doesn't update

### Want to Mix Professions?
Use the customization options in `contentConfig` to blend skills and content from multiple professions.

### Missing Skills for Your Field?
Add them to the `skills` arrays in `contentConfig` - they'll be added to the auto-generated ones.

## Examples in Action

### Switching from Developer to Designer

**Before (Developer):**
```typescript
profession: "developer"
// Automatically shows: "Programming & Development", "Architecture & Systems"
// Skills: JavaScript, React, Node.js, System Design
// Hero: "Building innovative software solutions"
```

**After (Designer):**
```typescript
profession: "designer"
// Automatically shows: "Design & User Experience", "Research & Strategy"  
// Skills: UI Design, UX Research, Prototyping, Figma
// Hero: "Creating beautiful, intuitive experiences"
```

Everything updates automatically - no need to manually change skills, descriptions, or content!

---

This multi-profession system makes the template truly universal while maintaining professional relevance for each field. Choose your profession and get a perfectly tailored portfolio in minutes!