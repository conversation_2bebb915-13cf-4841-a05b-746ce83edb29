---
// Scroll animations and micro-interactions
---

<script is:inline>
  // Scroll animations and micro-interactions
  (function() {
    if (typeof window === 'undefined') {
      return;
    }

    // Intersection Observer for scroll animations
    function setupScrollAnimations() {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
            entry.target.classList.remove('opacity-0');
          }
        });
      }, observerOptions);

      // Observe elements with animation classes
      const animatedElements = document.querySelectorAll('.animate-on-scroll, .fade-in-up, .slide-in-left, .slide-in-right');
      animatedElements.forEach((el) => {
        el.classList.add('opacity-0');
        observer.observe(el);
      });
    }

    // Smooth scroll for anchor links
    function setupSmoothScroll() {
      const anchorLinks = document.querySelectorAll('a[href^="#"]');
      anchorLinks.forEach((link) => {
        link.addEventListener('click', (e) => {
          const href = link.getAttribute('href');
          if (href && href !== '#') {
            const target = document.querySelector(href);
            if (target) {
              e.preventDefault();
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          }
        });
      });
    }

    // Parallax effect for hero sections
    function setupParallax() {
      const parallaxElements = document.querySelectorAll('.parallax');
      if (parallaxElements.length === 0) return;

      function updateParallax() {
        const scrolled = window.pageYOffset;
        parallaxElements.forEach((element) => {
          const rate = scrolled * -0.5;
          element.style.transform = `translateY(${rate}px)`;
        });
      }

      window.addEventListener('scroll', updateParallax);
    }

    // Initialize all animations
    function initializeAnimations() {
      setupScrollAnimations();
      setupSmoothScroll();
      setupParallax();
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeAnimations);
    } else {
      initializeAnimations();
    }

    // Re-initialize on page navigation (for SPAs)
    document.addEventListener('astro:page-load', initializeAnimations);
  })();
</script>

<style>
  /* Animation classes */
  .animate-on-scroll {
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .fade-in-up {
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .fade-in-up.animate-fade-in {
    transform: translateY(0);
  }

  .slide-in-left {
    transform: translateX(-30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .slide-in-left.animate-fade-in {
    transform: translateX(0);
  }

  .slide-in-right {
    transform: translateX(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .slide-in-right.animate-fade-in {
    transform: translateX(0);
  }

  /* Parallax elements */
  .parallax {
    transition: transform 0.1s ease-out;
  }
</style>