# Quick Reference Card

## 🚀 5-Minute Setup

### 1. Choose Your Profession
```typescript
// src/config/site.ts
profession: "developer" // Change this!
```

**Options:** `developer` | `designer` | `marketer` | `consultant` | `teacher`

### 2. Update Personal Info
```typescript
// src/config/site.ts
name: "Your Name",
email: "<EMAIL>",
jobTitle: "Your Professional Title",
location: "Your City, Country",
```

### 3. Deploy
```bash
npm run build
npm run preview  # Test locally
# Deploy to Netlify/Vercel
```

---

## 🎯 What Changes Automatically by Profession

| Profession | Skills Categories | Hero Message | Keywords |
|------------|------------------|--------------|----------|
| **Developer** | Programming & Development<br>Architecture & Systems | Building innovative software solutions | javascript, react, programming |
| **Designer** | Design & User Experience<br>Research & Strategy | Creating beautiful, intuitive experiences | ui ux designer, prototyping |
| **Marketer** | Marketing & Strategy<br>Analytics & Optimization | Driving growth through data-driven strategies | digital marketing, seo, content |
| **Consultant** | Strategy & Analysis<br>Process & Operations | Transforming businesses through strategic insights | business consultant, strategy |
| **Teacher** | Teaching & Curriculum<br>Learning & Assessment | Inspiring learning through innovative education | educator, curriculum, teaching |

---

## 🛠️ Quick Customizations

### Override Auto-Generated Skills
```typescript
// src/config/content.ts
skills: {
  primary: ["Your", "Custom", "Skills"], // Override
  secondary: dynamicContent.skills.secondary, // Keep auto-generated
}
```

### Custom Experience Highlights
```typescript
experienceHighlights: [
  { text: "Your custom achievement" },
  ...dynamicContent.experienceHighlights, // Mix with auto-generated
]
```

### Custom Bio
```typescript
// src/config/site.ts
bio: "Your custom bio instead of auto-generated"
```

---

## 📁 Key Files

| File | Purpose | Auto-Generated? |
|------|---------|-----------------|
| `src/config/site.ts` | Personal info, profession | ❌ Manual |
| `src/config/content.ts` | Skills, experience | ✅ Auto + customizable |
| `src/config/profession.ts` | Profession definitions | ✅ Pre-built |
| `PROFESSION_GUIDE.md` | Full documentation | 📖 Reference |

---

## 🆘 Common Issues

**Skills not updating?** → Restart dev server after changing profession

**Want to mix professions?** → Override specific skills in `content.ts`

**Add new profession?** → See `PROFESSION_GUIDE.md` section "Adding New Professions"

**Need help?** → Check `PROFESSION_GUIDE.md` for detailed examples

---

## 🎨 Example: Developer → Designer Switch

**Step 1:** Change profession
```typescript
// Before
profession: "developer"

// After  
profession: "designer"
```

**Result:** Automatic updates
- Skills: `JavaScript, React` → `UI Design, Prototyping`
- Categories: `Programming & Development` → `Design & User Experience`
- Hero: `Building software` → `Creating experiences`
- Keywords: `javascript, react` → `ui ux designer, prototyping`

**That's it!** No manual skill updates needed.

---

💡 **Pro Tip:** Start with the closest profession match, then customize individual sections as needed!