import { siteConfig } from './site';
import { getProfessionProfile } from './profession';

// Get profession-specific content
const professionProfile = getProfessionProfile(siteConfig.profession);

// Dynamic content generation based on profession
export const dynamicContent = {
  // Skills organized by profession
  skills: {
    primary: professionProfile.commonSkills.primary,
    secondary: professionProfile.commonSkills.secondary, 
    tools: professionProfile.commonSkills.tools,
    soft: professionProfile.commonSkills.soft,
  },

  // Skill category labels
  skillCategories: professionProfile.skillCategories,

  // Experience highlights
  experienceHighlights: professionProfile.experienceHighlights.map(text => ({ text })),

  // Professional content
  heroSubtitle: professionProfile.heroSubtitle,
  aboutDescription: professionProfile.aboutDescription,
  portfolioDescription: professionProfile.portfolioDescription,
  
  // CTA texts
  ctaText: professionProfile.ctaText,
  
  // SEO keywords
  seoKeywords: professionProfile.seoKeywords,
  
  // Resource descriptions
  resourcesDescription: professionProfile.resourcesDescription,
  
  // Project types
  projectTypes: professionProfile.projectTypes,

  // Auto-generate bio if not customized
  generatedBio: siteConfig.bio === "Brief professional description that will be auto-generated based on your profession, or customize here." 
    ? professionProfile.aboutDescription 
    : siteConfig.bio,

  // Auto-generate professional summary
  professionalSummary: `${professionProfile.name} with experience in ${professionProfile.commonSkills.primary.slice(0, 3).join(", ")} and a passion for ${professionProfile.heroSubtitle.toLowerCase()}.`,

  // Resume experience areas
  resumeExperienceAreas: [
    {
      title: professionProfile.skillCategories.primary,
      description: `Expertise in ${professionProfile.commonSkills.primary.slice(0, 3).join(", ")} and related technologies.`
    },
    {
      title: professionProfile.skillCategories.secondary,
      description: `Knowledge in ${professionProfile.commonSkills.secondary.slice(0, 3).join(", ")} and strategic approaches.`
    },
    {
      title: professionProfile.skillCategories.tools,
      description: `Proficiency with ${professionProfile.commonSkills.tools.slice(0, 3).join(", ")} and industry-standard platforms.`
    },
    {
      title: professionProfile.skillCategories.soft,
      description: `Strong ${professionProfile.commonSkills.soft.slice(0, 3).join(", ").toLowerCase()} and professional communication abilities.`
    }
  ]
};

// Override any skills from the original content config
export const mergedSkills = {
  primary: dynamicContent.skills.primary,
  secondary: dynamicContent.skills.secondary,
  tools: dynamicContent.skills.tools,
  soft: dynamicContent.skills.soft,
};

// Helper functions for easy access
export function getSkillsByCategory(category: keyof typeof dynamicContent.skills) {
  return dynamicContent.skills[category];
}

export function getAllSkills() {
  return Object.values(dynamicContent.skills).flat();
}

export function getExperienceHighlights() {
  return dynamicContent.experienceHighlights;
}

export function getProfessionalSummary() {
  return dynamicContent.professionalSummary;
}

export function getSkillCategoryName(category: keyof typeof dynamicContent.skillCategories) {
  return dynamicContent.skillCategories[category];
}