---
interface Props {
  title: string;
  content: string;
  icon?: string;
  imageUrl?: string;
  highlights?: string[];
  className?: string;
}

const { 
  title, 
  content, 
  icon = "📖", 
  imageUrl, 
  highlights,
  className = ""
} = Astro.props;
---

<div class={`story-card glass-card p-8 lg:p-10 group hover:-translate-y-2 transition-all duration-500 ${className}`}>
  <!-- Header with icon and title -->
  <div class="flex items-start gap-4 mb-6">
    {imageUrl ? (
      <div class="flex-shrink-0 w-16 h-16 rounded-2xl overflow-hidden shadow-lg">
        <img 
          src={imageUrl} 
          alt={`${title} illustration`}
          class="w-full h-full object-cover"
          loading="lazy"
        />
      </div>
    ) : (
      <div class="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 dark:from-primary-400 dark:to-primary-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
        <span class="text-2xl">{icon}</span>
      </div>
    )}
    
    <div class="flex-1 min-w-0">
      <h3 class="text-2xl lg:text-3xl font-bold text-text-light dark:text-text-dark font-heading group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
        {title}
      </h3>
      <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded mt-2 group-hover:w-20 transition-all duration-500"></div>
    </div>
  </div>

  <!-- Content -->
  <div class="mb-6">
    {content.split('\n\n').map((paragraph) => (
      <p class="text-text-light-muted dark:text-text-dark-muted text-lg leading-relaxed mb-4 last:mb-0">
        {paragraph}
      </p>
    ))}
  </div>

  <!-- Highlights -->
  {highlights && highlights.length > 0 && (
    <div class="bg-gradient-to-r from-primary-50/50 to-accent-50/50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-6 border border-primary-200/30 dark:border-primary-700/30">
      <h4 class="text-lg font-semibold text-primary-700 dark:text-primary-300 mb-4 font-heading flex items-center gap-3">
        <span class="w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white text-xs">⭐</span>
        Key Points
      </h4>
      <ul class="space-y-3">
        {highlights.map((highlight) => (
          <li class="flex items-start gap-3">
            <span class="text-primary-600 dark:text-primary-400 mt-1 font-bold text-lg flex-shrink-0">✓</span>
            <span class="text-text-light-muted dark:text-text-dark-muted leading-relaxed">{highlight}</span>
          </li>
        ))}
      </ul>
    </div>
  )}
</div>

<style>
.story-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(255, 255, 255, 0.90) 50%, 
    rgba(248, 250, 252, 0.90) 100%
  );
  border: 1px solid rgba(203, 213, 225, 0.3);
  backdrop-filter: blur(12px);
  box-shadow: 
    0 10px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.dark .story-card {
  background: linear-gradient(135deg, 
    rgba(30, 41, 59, 0.95) 0%, 
    rgba(30, 41, 59, 0.90) 50%, 
    rgba(15, 23, 42, 0.90) 100%
  );
  border: 1px solid rgba(71, 85, 105, 0.4);
  box-shadow: 
    0 10px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.story-card:hover {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.dark .story-card:hover {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}
</style>